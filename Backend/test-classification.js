// Test script to verify the improved classification logic
const { OpenAIService } = require('./dist/services/openai/index.js')

async function testClassification() {
  console.log('🧪 Testing Message Classification...\n')
  
  const testMessages = [
    {
      message: "I'm experiencing a bug where the login button doesn't work. I've tried multiple browsers and cleared my cache but the issue persists. This is preventing me from accessing my account.",
      expected: "issue"
    },
    {
      message: "Hello, I need help with my account settings",
      expected: "inquiry"
    },
    {
      message: "The payment system is not working properly",
      expected: "issue"
    },
    {
      message: "What are your business hours?",
      expected: "inquiry"
    }
  ]
  
  for (const test of testMessages) {
    try {
      const result = await OpenAIService.processMessage(test.message)
      const isCorrect = result.classification.type === test.expected
      console.log(`📝 Message: "${test.message.substring(0, 50)}..."`)
      console.log(`🎯 Expected: ${test.expected}`)
      console.log(`✅ Classified: ${result.classification.type} (confidence: ${result.classification.confidence})`)
      console.log(`📊 Result: ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`)
      console.log(`💬 Response: ${result.message.substring(0, 100)}...`)
      console.log('---\n')
    } catch (error) {
      console.error(`❌ Error testing message: ${error.message}`)
    }
  }
}

testClassification().catch(console.error)
