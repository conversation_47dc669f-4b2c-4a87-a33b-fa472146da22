// Simple test script to verify the chat system setup
const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
require('dotenv').config()

const app = express()
const PORT = process.env.PORT || 3000

// Middleware
app.use(helmet())
app.use(cors())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    features: {
      openai: !!process.env.OPENAI_API_KEY,
      jira: !!(process.env.JIRA_BASE_URL && process.env.JIRA_USERNAME && process.env.JIRA_API_TOKEN),
      speech: !!(process.env.GOOGLE_APPLICATION_CREDENTIALS && process.env.GOOGLE_CLOUD_PROJECT_ID),
      supabase: !!(process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_KEY)
    }
  })
})

// Test endpoints
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Ava Chat System Backend is running!',
    endpoints: {
      auth: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
        logout: 'POST /api/auth/logout',
        profile: 'GET /api/auth/profile',
        updateProfile: 'PUT /api/auth/profile',
        refresh: 'POST /api/auth/refresh'
      },
      chat: {
        sendMessage: 'POST /api/chat/send-message',
        getMessages: 'GET /api/chat/conversation/:id/messages',
        getConversations: 'GET /api/chat/conversations',
        getCurrentConversation: 'GET /api/chat/current-conversation'
      },
      support: {
        getConversations: 'GET /api/support/conversations',
        assignConversation: 'POST /api/support/conversations/:id/assign',
        closeConversation: 'POST /api/support/conversations/:id/close',
        getJiraTicket: 'GET /api/support/jira-tickets/:id',
        updateJiraStatus: 'PUT /api/support/jira-tickets/:id/status',
        addJiraComment: 'POST /api/support/jira-tickets/:id/comments',
        sendMessage: 'POST /api/support/conversations/:id/messages'
      }
    }
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' })
})

// Global error handler
app.use((err, req, res, next) => {
  console.error('Global error handler:', err)
  res.status(500).json({ 
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message 
  })
})

// Start the server
app.listen(PORT, () => {
  console.log(`🚀 Ava Chat System Backend running on port ${PORT}`)
  console.log(`📚 Health check: http://localhost:${PORT}/health`)
  console.log(`🧪 Test endpoint: http://localhost:${PORT}/api/test`)
  console.log(`\n📋 Next steps:`)
  console.log(`1. Install dependencies: npm install`)
  console.log(`2. Set up environment variables in .env`)
  console.log(`3. Run database migrations from db/create-complete-schema.sql`)
  console.log(`4. Start the full application: npm run dev`)
})

module.exports = app
