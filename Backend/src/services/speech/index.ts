import fs from 'fs';
import vosk from 'vosk';
import path from 'path';

const MODEL_PATH = path.join(__dirname, 'model'); // You must download a Vosk model and place it here
const SAMPLE_RATE = 16000;  

let model: vosk.Model | null = null;
if (fs.existsSync(MODEL_PATH)) {
  model = new vosk.Model(MODEL_PATH);
} else {
  console.warn('⚠️  Vosk model not found. Please download a model and place it in src/services/speech/model');
}

export class SpeechService {
  static async transcribeAudioFile(audioFilePath: string): Promise<string> {
    if (!model) throw new Error('Vosk model not loaded.');
    const wav = require('wav');
    return new Promise((resolve, reject) => {
      const wfReader = new wav.Reader();
      const rec = new vosk.Recognizer({ model, sampleRate: SAMPLE_RATE });
      const stream = fs.createReadStream(audioFilePath);
      wfReader.on('format', (format: any) => {
        if (format.sampleRate !== SAMPLE_RATE) {
          reject(new Error(`Audio file must be ${SAMPLE_RATE}Hz sample rate`));
        }
      });
      wfReader.on('data', (data: Buffer) => {
        rec.acceptWaveform(data);
      });
      wfReader.on('end', () => {
        const result = rec.finalResult();
        rec.free();
        resolve(result.text || '');
      });
      stream.pipe(wfReader);
    });
  }

  static validateAudioFile(file: Express.Multer.File): { valid: boolean; error?: string } {
    const allowedMimeTypes = [
      'audio/wav',
      'audio/x-wav',
      'audio/wave',
      'audio/x-pn-wav',
      'audio/webm',
      'audio/mp3',
      'audio/mpeg',
      'audio/flac',
      'audio/ogg'
    ];
    const maxSize = parseInt(process.env.MAX_FILE_SIZE || '10485760');
    if (!allowedMimeTypes.includes(file.mimetype)) {
      return {
        valid: false,
        error: `Unsupported audio format. Allowed formats: ${allowedMimeTypes.join(', ')}`
      };
    }
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File too large. Maximum size: ${maxSize / 1024 / 1024}MB`
      };
    }
    return { valid: true };
  }
}


