import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import dotenv from 'dotenv'
import { supabaseAdmin } from '@/lib'
import authRoutes from './features/auth/routes'
import refreshRoutes from './features/auth/refresh'
import chatRoutes from './features/chat/routes'
import supportRoutes from './features/chat/support-routes'
import speechRoutes from './features/speech/routes'

// Load environment variables
dotenv.config()

const app = express()
const PORT = process.env.PORT || 3000

// Middleware
app.use(helmet())
app.use(cors({
  origin: process.env.FRONTEND_URL || '*',
  credentials: true
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  })
})

// API routes
app.use('/api/auth', authRoutes)
app.use('/api/auth', refreshRoutes)
app.use('/api/chat', chatRoutes)
app.use('/api/support', supportRoutes)
app.use('/api/speech', speechRoutes)


// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' })
})

// Global error handler
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Global error handler:', err)
  res.status(500).json({ 
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message 
  })
})

// Initialize Supabase listeners
async function initializeApp() {
  try {
    // Supabase connectivity check
    const { error } = await supabaseAdmin.from('').select('*').limit(1)
    if (error) {
      console.error('❌ Supabase connection failed:', error)
      process.exit(1)
    } else {
      console.log('✅ Supabase connection successful')
    }

    // Initialize event listeners
    
    // Start the server
    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`)
      console.log(`📚 Health check: http://localhost:${PORT}/health`)
    })
  } catch (error) {
    console.error('Failed to initialize app:', error)
    process.exit(1)
  }
}


// Start the application
initializeApp()
