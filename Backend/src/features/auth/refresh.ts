import jwt from 'jsonwebtoken'
import { supabaseAdmin } from '@/lib/supabase'
import { Router } from 'express'

const router = Router()

// Refresh token endpoint
router.post('/refresh', async (req, res) => {
  const { refreshToken } = req.body
  if (!refreshToken) return res.status(400).json({ error: 'Missing refresh token' })
  try {
    const payload = jwt.verify(refreshToken, process.env.JWT_SECRET!) as any
    // Get user profile
    const { data: profile, error: profileError } = await supabaseAdmin.from('profiles').select('*').eq('id', payload.sub).single()
    if (profileError || !profile) return res.status(401).json({ error: 'Profile not found' })
    // Issue new access token
    const accessToken = jwt.sign({ sub: payload.sub, role: profile.role }, process.env.JWT_SECRET!, { expiresIn: '1h' })
    res.json({ accessToken })
  } catch (err) {
    return res.status(401).json({ error: 'Invalid or expired refresh token' })
  }
})

export default router