import { Router } from 'express'
import { supabaseAdmin } from '@/lib/supabase'
import jwt from 'jsonwebtoken'
import { requireAuth, AuthenticatedRequest } from './index'

const router = Router()

// Register endpoint
router.post('/register', async (req, res) => {
  const { email, password, name, role, avatar_url } = req.body
  if (!email || !password || !name || !role) {
    return res.status(400).json({ error: 'Missing required fields' })
  }
  // Create user in Supabase Auth
  const { data, error } = await supabaseAdmin.auth.admin.createUser({
    email,
    password,
    email_confirm: true
  })
  if (error || !data.user) return res.status(400).json({ error: error?.message || 'User creation failed' })

  // Insert profile
  const { error: profileError } = await supabaseAdmin.from('profiles').insert({
    id: data.user.id,
    role,
    name,
    avatar_url
  })
  if (profileError) return res.status(400).json({ error: profileError.message })

  // Generate JWT
  const accessToken = jwt.sign({ sub: data.user.id, role }, process.env.JWT_SECRET!, { expiresIn: '1h' })
  const refreshToken = jwt.sign({ sub: data.user.id }, process.env.JWT_SECRET!, { expiresIn: '7d' })

  res.json({ accessToken, refreshToken })
})

// Login endpoint
router.post('/login', async (req, res) => {
  const { email, password } = req.body
  if (!email || !password) return res.status(400).json({ error: 'Missing email or password' })

  // Authenticate with Supabase
  const { data, error } = await supabaseAdmin.auth.signInWithPassword({ email, password })
  if (error || !data.user) return res.status(401).json({ error: error?.message || 'Invalid credentials' })

  // Get profile
  const { data: profile, error: profileError } = await supabaseAdmin.from('profiles').select('*').eq('id', data.user.id).single()
  if (profileError || !profile) return res.status(401).json({ error: 'Profile not found' })

  // Generate JWT
  const accessToken = jwt.sign({ sub: data.user.id, role: profile.role }, process.env.JWT_SECRET!, { expiresIn: '1h' })
  const refreshToken = jwt.sign({ sub: data.user.id }, process.env.JWT_SECRET!, { expiresIn: '7d' })

  res.json({ accessToken, refreshToken })
})

// Logout endpoint
router.post('/logout', requireAuth, async (req: AuthenticatedRequest, res) => {
  // In a stateless JWT system, logout is handled client-side by removing the token
  // We could implement a token blacklist here if needed
  res.json({ message: 'Logged out successfully' })
})

// Get profile endpoint
router.get('/profile', requireAuth, async (req: AuthenticatedRequest, res) => {
  try {
    const { data: profile, error } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', req.user!.sub)
      .single()

    if (error || !profile) {
      return res.status(404).json({ error: 'Profile not found' })
    }

    res.json(profile)
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch profile' })
  }
})

// Update profile endpoint
router.put('/profile', requireAuth, async (req: AuthenticatedRequest, res) => {
  try {
    const { name, avatar_url } = req.body
    const userId = req.user!.sub

    const updateData: any = {}
    if (name !== undefined) updateData.name = name
    if (avatar_url !== undefined) updateData.avatar_url = avatar_url

    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({ error: 'No fields to update' })
    }

    const { data: profile, error } = await supabaseAdmin
      .from('profiles')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      return res.status(400).json({ error: error.message })
    }

    res.json(profile)
  } catch (error) {
    res.status(500).json({ error: 'Failed to update profile' })
  }
})

export default router
