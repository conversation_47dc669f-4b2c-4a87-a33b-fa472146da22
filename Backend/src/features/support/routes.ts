import { Router } from 'express'
import { z } from 'zod'
import { supabase } from '../../lib/supabase'

const router = Router()

// Validation schemas
const validateSupportCodeSchema = z.object({
  supportCode: z.string().min(1, 'Support code is required'),
})

const generateSupportCodeSchema = z.object({
  description: z.string().optional(),
  expiresAt: z.string().optional(),
  maxUses: z.number().min(1).optional(),
})

// Validate support code endpoint
router.post('/validate-code', async (req, res) => {
  try {
    const { supportCode } = validateSupportCodeSchema.parse(req.body)

    // Call the database function to validate the support code
    const { data, error } = await supabase.rpc('validate_support_code', {
      p_support_code: supportCode
    })

    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ 
        error: 'Failed to validate support code' 
      })
    }

    if (!data) {
      return res.status(400).json({ 
        error: 'Invalid or expired support code' 
      })
    }

    res.json({ 
      valid: true, 
      message: 'Support code is valid' 
    })
  } catch (error) {
    console.error('Validation error:', error)
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: error.errors 
      })
    }

    res.status(500).json({ 
      error: 'Internal server error' 
    })
  }
})

// Generate support code endpoint (requires support user authentication)
router.post('/generate-code', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authorization token required' })
    }

    const token = authHeader.split(' ')[1]
    
    // Verify the user is authenticated and is a support user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' })
    }

    // Check if user is support type
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', user.id)
      .single()

    if (profileError || profile?.user_type !== 'support') {
      return res.status(403).json({ error: 'Access denied: Support users only' })
    }

    const { description, expiresAt, maxUses } = generateSupportCodeSchema.parse(req.body)

    // Generate a new support code
    const { data, error } = await supabase.rpc('generate_support_code', {
      p_created_by: user.id,
      p_description: description || null,
      p_expires_at: expiresAt || null,
      p_max_uses: maxUses || null
    })

    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ 
        error: 'Failed to generate support code' 
      })
    }

    res.json({ 
      code: data,
      message: 'Support code generated successfully' 
    })
  } catch (error) {
    console.error('Generation error:', error)
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: error.errors 
      })
    }

    res.status(500).json({ 
      error: 'Internal server error' 
    })
  }
})

// Get support codes endpoint (requires support user authentication)
router.get('/codes', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authorization token required' })
    }

    const token = authHeader.split(' ')[1]
    
    // Verify the user is authenticated and is a support user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' })
    }

    // Check if user is support type
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', user.id)
      .single()

    if (profileError || profile?.user_type !== 'support') {
      return res.status(403).json({ error: 'Access denied: Support users only' })
    }

    // Get all support codes
    const { data, error } = await supabase.rpc('get_support_codes')

    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ 
        error: 'Failed to fetch support codes' 
      })
    }

    res.json({ codes: data })
  } catch (error) {
    console.error('Fetch error:', error)
    res.status(500).json({ 
      error: 'Internal server error' 
    })
  }
})

// Deactivate support code endpoint
router.post('/deactivate-code', async (req, res) => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authorization token required' })
    }

    const token = authHeader.split(' ')[1]
    
    // Verify the user is authenticated and is a support user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' })
    }

    // Check if user is support type
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', user.id)
      .single()

    if (profileError || profile?.user_type !== 'support') {
      return res.status(403).json({ error: 'Access denied: Support users only' })
    }

    const { supportCode } = z.object({
      supportCode: z.string().min(1, 'Support code is required')
    }).parse(req.body)

    // Deactivate the support code
    const { error } = await supabase.rpc('deactivate_support_code', {
      p_support_code: supportCode
    })

    if (error) {
      console.error('Database error:', error)
      return res.status(500).json({ 
        error: 'Failed to deactivate support code' 
      })
    }

    res.json({ 
      message: 'Support code deactivated successfully' 
    })
  } catch (error) {
    console.error('Deactivation error:', error)
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        error: 'Invalid request data',
        details: error.errors 
      })
    }

    res.status(500).json({ 
      error: 'Internal server error' 
    })
  }
})

export { router as supportRoutes }