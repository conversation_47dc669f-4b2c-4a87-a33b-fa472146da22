import { Router } from 'express';
import { upload, handleUploadError, validateAudioFile, cleanupTempFiles } from '@/middleware/upload';
import { SpeechService } from '@/services/speech';
import fs from 'fs';

const router = Router();

router.post('/speech-to-text',
  upload.single('audio'),
  handleUploadError,
  validateAudioFile,
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No audio file uploaded' });
      }
      const text = await SpeechService.transcribeAudioFile(req.file.path);
      // Clean up uploaded file
      fs.unlinkSync(req.file.path);
      res.json({ text });
    } catch (error) {
      console.error('Speech-to-text error:', error);
      res.status(500).json({ error: 'Failed to transcribe audio', message: error.message });
    }
  },
  cleanupTempFiles
);

export default router;


