{"name": "ava-chatbot-backend", "version": "1.0.0", "description": "<PERSON>end with Jira Integration and Human Support", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["chatbot", "jira", "support", "supabase", "firebase", "notifications"], "author": "Ava Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.4", "@xenova/transformers": "^2.17.1", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.11.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "openai": "^4.20.1", "uuid": "^9.0.1", "vosk": "^0.3.39", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^2.0.0", "@types/node": "^20.10.4", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}