# Ava Chat System - Postman Collection

## Base URL
```
http://localhost:3000
```

## Environment Variables (Set these in Postman)
```
base_url: http://localhost:3000
access_token: (will be set after login)
refresh_token: (will be set after login)
```

---

## 🔐 Authentication Endpoints

### 1. Register User
```http
POST {{base_url}}/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "<PERSON>",
  "role": "user",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

### 2. Register Support Team Member
```http
POST {{base_url}}/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "Support Agent",
  "role": "support",
  "avatar_url": "https://example.com/support-avatar.jpg"
}
```

### 3. Login User
```http
POST {{base_url}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 4. Login Support Team Member
```http
POST {{base_url}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 5. Refresh Token
```http
POST {{base_url}}/api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "{{refresh_token}}"
}
```

### 6. Logout
```http
POST {{base_url}}/api/auth/logout
Authorization: Bearer {{access_token}}
```

### 7. Get Profile
```http
GET {{base_url}}/api/auth/profile
Authorization: Bearer {{access_token}}
```

### 8. Update Profile
```http
PUT {{base_url}}/api/auth/profile
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "name": "Updated Name",
  "avatar_url": "https://example.com/new-avatar.jpg"
}
```

---

## 💬 Chat Endpoints (User)

### 9. Send Text Message
```http
POST {{base_url}}/api/chat/send-message
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "message": "Hello, I need help with my account settings"
}
```

### 10. Send Text Message (Issue)
```http
POST {{base_url}}/api/chat/send-message
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "message": "I'm experiencing a bug where the login button doesn't work. I've tried multiple browsers and cleared my cache but the issue persists. This is preventing me from accessing my account."
}
```

### 11. Send Text Message (General Inquiry)
```http
POST {{base_url}}/api/chat/send-message
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "message": "What are your business hours? How can I contact customer support?"
}
```

### 12. Send Voice Message
```http
POST {{base_url}}/api/chat/send-message
Authorization: Bearer {{access_token}}
Content-Type: multipart/form-data

audio: [Select audio file - WebM, MP3, WAV, FLAC, OGG]
```

### 13. Get Current Conversation
```http
GET {{base_url}}/api/chat/current-conversation
Authorization: Bearer {{access_token}}
```

### 14. Get Conversation Messages
```http
GET {{base_url}}/api/chat/conversation/{{conversation_id}}/messages
Authorization: Bearer {{access_token}}
```

### 15. Get User Conversations
```http
GET {{base_url}}/api/chat/conversations
Authorization: Bearer {{access_token}}
```

---

## 🛠 Support Panel Endpoints (Support Team Only)

### 16. Get All Open Conversations
```http
GET {{base_url}}/api/support/conversations?status=open
Authorization: Bearer {{support_access_token}}
```

### 17. Get Assigned Conversations
```http
GET {{base_url}}/api/support/conversations?status=assigned
Authorization: Bearer {{support_access_token}}
```

### 18. Assign Conversation to Support Agent
```http
POST {{base_url}}/api/support/conversations/{{conversation_id}}/assign
Authorization: Bearer {{support_access_token}}
```

### 19. Send Support Message
```http
POST {{base_url}}/api/support/conversations/{{conversation_id}}/messages
Authorization: Bearer {{support_access_token}}
Content-Type: application/json

{
  "message": "Hello! I'm looking into your issue. Can you provide more details about when this problem started?"
}
```

### 20. Get Jira Ticket Details
```http
GET {{base_url}}/api/support/jira-tickets/{{jira_ticket_id}}
Authorization: Bearer {{support_access_token}}
```

### 21. Update Jira Ticket Status
```http
PUT {{base_url}}/api/support/jira-tickets/{{jira_ticket_id}}/status
Authorization: Bearer {{support_access_token}}
Content-Type: application/json

{
  "status": "In Progress"
}
```

### 22. Add Comment to Jira Ticket
```http
POST {{base_url}}/api/support/jira-tickets/{{jira_ticket_id}}/comments
Authorization: Bearer {{support_access_token}}
Content-Type: application/json

{
  "comment": "Issue confirmed. Working on a fix. Expected resolution time: 2-3 business days."
}
```

### 23. Close Conversation
```http
POST {{base_url}}/api/support/conversations/{{conversation_id}}/close
Authorization: Bearer {{support_access_token}}
Content-Type: application/json

{
  "jiraTicketId": "{{jira_ticket_id}}"
}
```

---

## 🧪 Test Endpoints

### 24. Health Check
```http
GET {{base_url}}/health
```

### 25. API Test Endpoint
```http
GET {{base_url}}/api/test
```

---

## 📋 Postman Collection Setup Instructions

### 1. Create Environment Variables
In Postman, create a new environment with these variables:
- `base_url`: `http://localhost:3000`
- `access_token`: (leave empty, will be set by login)
- `refresh_token`: (leave empty, will be set by login)
- `support_access_token`: (leave empty, will be set by support login)
- `conversation_id`: (leave empty, will be set from responses)
- `jira_ticket_id`: (leave empty, will be set from responses)

### 2. Test Scripts for Login Endpoints
Add this test script to your login requests to automatically set tokens:

```javascript
// For regular user login
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("access_token", response.accessToken);
    pm.environment.set("refresh_token", response.refreshToken);
}

// For support login
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("support_access_token", response.accessToken);
}
```

### 3. Test Scripts for Chat Endpoints
Add this test script to send-message requests to capture conversation_id:

```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("conversation_id", response.conversationId);
    if (response.jiraTicketId) {
        pm.environment.set("jira_ticket_id", response.jiraTicketId);
    }
}
```

---

## 🎯 Testing Workflow

### Step 1: Setup
1. Start your server: `npm run dev`
2. Test health endpoint: `GET /health`
3. Register a user and support agent
4. Login both users

### Step 2: Test Chat Flow
1. Send a text message as a user
2. Check the AI response
3. Send a voice message (upload audio file)
4. Send an issue message to trigger Jira ticket creation
5. Get conversation messages

### Step 3: Test Support Flow
1. Login as support agent
2. Get all open conversations
3. Assign a conversation to yourself
4. Send a support message
5. Check Jira ticket details
6. Update ticket status
7. Add comment to ticket
8. Close the conversation

### Step 4: Test Edge Cases
1. Try sending message without authentication
2. Try accessing support endpoints as regular user
3. Try sending invalid file types
4. Test with malformed JSON

---

## 📝 Sample Test Data

### Sample Audio Files
For voice message testing, you can use:
- WebM files (recommended)
- MP3 files
- WAV files
- Keep files under 10MB

### Sample Messages for Testing

**General Inquiry:**
```json
{
  "message": "What are your business hours and how can I reach customer support?"
}
```

**Issue Report:**
```json
{
  "message": "I'm experiencing a critical bug where the payment system is not processing transactions. I've tried multiple payment methods and browsers. This is blocking my business operations."
}
```

**Feature Request:**
```json
{
  "message": "I would like to request a dark mode feature for the mobile app. This would greatly improve user experience during night usage."
}
```

---

## 🔍 Expected Responses

### Successful Login Response:
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Successful Message Response:
```json
{
  "success": true,
  "conversationId": "123e4567-e89b-12d3-a456-************",
  "userMessage": {
    "id": "123e4567-e89b-12d3-a456-************",
    "content": "Hello, I need help with my account",
    "type": "text",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "aiResponse": {
    "id": "123e4567-e89b-12d3-a456-************",
    "content": "Hello! I'd be happy to help you with your account. What specific issue are you experiencing?",
    "timestamp": "2024-01-15T10:30:01Z"
  },
  "jiraTicketId": "PROJ-123"
}
```

### Error Response:
```json
{
  "error": "Unauthorized",
  "message": "Invalid or expired token"
}
```

This collection covers all the endpoints with proper request bodies for comprehensive testing in Postman!
